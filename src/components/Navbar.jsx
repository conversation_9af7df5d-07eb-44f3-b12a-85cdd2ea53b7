import { useState } from 'react';
import toast from 'react-hot-toast';
import useScrollDirection from '../hooks/useScrollDirection';
import { useWeb3 } from '../hooks/useWeb3';

const Navbar = () => {
  const [showWalletModal, setShowWalletModal] = useState(false);

  // 滚动方向检测
  const { isVisible: isNavbarVisible } = useScrollDirection(30);

  // Web3钱包功能
  const {
    account,
    isConnecting,
    isCorrectChain,
    connectWallet,
    disconnect,
    switchToCorrectChain
  } = useWeb3();

  // 格式化钱包地址显示
  const formatAddress = (address) => {
    if (!address) return '';
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const handleWalletClick = async () => {
    if (account) {
      // 如果已连接，显示钱包弹窗
      setShowWalletModal(true);
    } else {
      // 如果未连接，连接钱包
      await connectWallet();
    }
  };

  const handleDisconnect = () => {
    disconnect();
    setShowWalletModal(false);
  };

  const handleSwitchChain = async () => {
    await switchToCorrectChain();
  };

  const copyAddress = async () => {
    if (account) {
      try {
        await navigator.clipboard.writeText(account);
        toast.success('钱包地址已复制到剪贴板');
      } catch (err) {
        console.error('复制失败:', err);
        toast.error('复制失败');
      }
    }
  };

  return (
    <nav
        className="fixed top-0 left-0 right-0 z-50 border-b border-gray-800"
        style={{
          backgroundColor: 'var(--color-bg-primary)',
          transform: isNavbarVisible ? 'translateY(0)' : 'translateY(-100%)',
          transition: 'transform 0.3s ease-in-out',
          width: '100%',
          maxWidth: '100vw'
        }}
      >
      <div className="mobile-container">
        <div className="flex justify-between items-center h-16 px-4">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <img
              src="/vite.svg"
              alt="Logo"
              className="w-8 h-8"
            />
            <span className="text-xl font-bold" style={{ color: 'var(--color-text-primary)' }}>
              Binary Options
            </span>
          </div>

          {/* Wallet Connection */}
          <div className="hidden md:flex items-center space-x-4">
            <div
              onClick={handleWalletClick}
              className="px-4 py-2 rounded-lg text-sm font-normal cursor-pointer transition-all border hover:bg-opacity-10"
              style={{
                color: 'var(--color-text-primary)',
                borderColor: 'var(--color-accent)',
                backgroundColor: 'transparent'
              }}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = 'rgba(234, 174, 54, 0.1)';
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = 'transparent';
              }}
            >
              {isConnecting ? '连接中...' : account ? formatAddress(account) : '连接钱包'}
            </div>
          </div>

          {/* 移动端也显示钱包按钮 */}
          <div className="md:hidden">
            <div
              onClick={handleWalletClick}
              className="px-3 py-2 rounded-lg text-sm font-normal cursor-pointer transition-all border"
              style={{
                color: 'var(--color-text-primary)',
                borderColor: 'var(--color-accent)',
                backgroundColor: 'transparent'
              }}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = 'rgba(234, 174, 54, 0.1)';
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = 'transparent';
              }}
            >
              {isConnecting ? '连接中...' : account ? formatAddress(account) : '连接钱包'}
            </div>
          </div>
        </div>
      </div>

      {/* 钱包弹窗 */}
      {showWalletModal && account && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center"
          style={{ backgroundColor: 'rgba(0, 0, 0, 0.8)' }}
          onClick={() => setShowWalletModal(false)}
        >
          <div
            className="relative rounded-2xl p-6 m-4 max-w-sm w-full"
            style={{ backgroundColor: 'var(--color-bg-secondary)' }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* 顶部栏：切换网络按钮和关闭按钮 */}
            <div className="flex justify-between items-center mb-6">
              {!isCorrectChain && (
                <button
                  onClick={handleSwitchChain}
                  className="flex items-center px-3 py-1 rounded-lg text-sm transition-all"
                  style={{
                    backgroundColor: '#ff4444',
                    color: 'white'
                  }}
                >
                  <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" className="mr-1">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                  </svg>
                  切换网络
                </button>
              )}
              {isCorrectChain && <div></div>}

              <button
                onClick={() => setShowWalletModal(false)}
                className="w-8 h-8 rounded-full flex items-center justify-center transition-all"
                style={{
                  backgroundColor: 'var(--color-bg-primary)',
                  color: '#8f8f8f'
                }}
              >
                ✕
              </button>
            </div>

            {/* 钱包头像 */}
            <div className="flex flex-col items-center mb-6">
              <div
                className="w-16 h-16 rounded-full mb-4 flex items-center justify-center"
                style={{ backgroundColor: '#eaae36' }}
              >
                <svg width="32" height="32" fill="white" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </div>

              {/* 钱包地址 */}
              <div
                className="text-lg font-medium mb-2 cursor-pointer flex items-center"
                style={{ color: 'var(--color-text-primary)' }}
                onClick={copyAddress}
              >
                {formatAddress(account)}
                <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" className="ml-2">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              </div>

              {/* 余额显示 */}
              <div className="text-sm" style={{ color: '#8f8f8f' }}>
                0.000 BNB
              </div>
            </div>

            {/* 功能按钮 */}
            <div className="space-y-3">
              <button
                className="w-full flex items-center justify-between p-3 rounded-lg transition-all"
                style={{ backgroundColor: 'var(--color-bg-primary)' }}
              >
                <div className="flex items-center">
                  <div
                    className="w-8 h-8 rounded-lg mr-3 flex items-center justify-center"
                    style={{ backgroundColor: '#4f46e5' }}
                  >
                    <svg width="16" height="16" fill="white" viewBox="0 0 24 24">
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                  </div>
                  <span style={{ color: 'var(--color-text-primary)' }}>Send</span>
                </div>
                <svg width="16" height="16" fill="none" stroke="#8f8f8f" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>

              <button
                className="w-full flex items-center justify-between p-3 rounded-lg transition-all"
                style={{ backgroundColor: 'var(--color-bg-primary)' }}
              >
                <div className="flex items-center">
                  <div
                    className="w-8 h-8 rounded-lg mr-3 flex items-center justify-center"
                    style={{ backgroundColor: '#059669' }}
                  >
                    <svg width="16" height="16" fill="white" viewBox="0 0 24 24">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                  </div>
                  <span style={{ color: 'var(--color-text-primary)' }}>Activity</span>
                </div>
                <svg width="16" height="16" fill="none" stroke="#8f8f8f" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>

              <button
                onClick={handleDisconnect}
                className="w-full flex items-center justify-between p-3 rounded-lg transition-all"
                style={{ backgroundColor: 'var(--color-bg-primary)' }}
              >
                <div className="flex items-center">
                  <div
                    className="w-8 h-8 rounded-lg mr-3 flex items-center justify-center"
                    style={{ backgroundColor: '#dc2626' }}
                  >
                    <svg width="16" height="16" fill="white" viewBox="0 0 24 24">
                      <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5z"/>
                    </svg>
                  </div>
                  <span style={{ color: 'var(--color-text-primary)' }}>Disconnect</span>
                </div>
              </button>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
